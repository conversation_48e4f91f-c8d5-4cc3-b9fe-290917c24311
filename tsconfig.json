{"compilerOptions": {"target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@secure-vault/shared-types": ["./packages/shared-types/src/index"], "@secure-vault/crypto-core": ["./packages/crypto-core/src/index"], "@secure-vault/ui-components": ["./packages/ui-components/src/index"], "@secure-vault/api-client": ["./packages/api-client/src/index"], "@secure-vault/config": ["./packages/config/src/index"], "@secure-vault/database": ["./libs/database/src/index"], "@secure-vault/auth": ["./libs/auth/src/index"], "@secure-vault/testing": ["./libs/testing/src/index"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}