export interface CryptoConfig {
  algorithm: string;
  keyLength: number;
  iterations: number;
  saltLength: number;
}

export interface EncryptedData {
  data: ArrayBuffer;
  iv: ArrayBuffer;
  salt?: ArrayBuffer;
}

export interface KeyPair {
  publicKey: CryptoKey;
  privateKey: CryptoKey;
}

export interface KeyDerivationParams {
  salt: string;
  iterations: number;
  algorithm: string;
}

export interface EncryptionResult {
  encryptedData: ArrayBuffer;
  iv: ArrayBuffer;
  authTag?: ArrayBuffer;
}

export interface DecryptionParams {
  encryptedData: ArrayBuffer;
  iv: A<PERSON>yBuffer;
  key: CryptoKey;
  authTag?: ArrayBuffer;
}