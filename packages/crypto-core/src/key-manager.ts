import { KeyDerivationParams } from './types';

export class KeyManager {
  private static readonly DB_NAME = 'SecureFileVaultKeys';
  private static readonly DB_VERSION = 1;
  private static readonly STORE_NAME = 'keys';

  /**
   * Initialize IndexedDB for key storage
   */
  private static async initDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(KeyManager.DB_NAME, KeyManager.DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(KeyManager.STORE_NAME)) {
          db.createObjectStore(KeyManager.STORE_NAME, { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Store encrypted master key
   */
  static async storeMasterKey(userId: string, encryptedKey: string, params: KeyDerivationParams): Promise<void> {
    const db = await KeyManager.initDB();
    const transaction = db.transaction([KeyManager.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(KeyManager.STORE_NAME);

    const keyData = {
      id: `master_${userId}`,
      encryptedKey,
      params,
      timestamp: Date.now(),
    };

    return new Promise((resolve, reject) => {
      const request = store.put(keyData);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  /**
   * Retrieve encrypted master key
   */
  static async getMasterKey(userId: string): Promise<{ encryptedKey: string; params: KeyDerivationParams } | null> {
    const db = await KeyManager.initDB();
    const transaction = db.transaction([KeyManager.STORE_NAME], 'readonly');
    const store = transaction.objectStore(KeyManager.STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.get(`master_${userId}`);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          resolve({
            encryptedKey: result.encryptedKey,
            params: result.params,
          });
        } else {
          resolve(null);
        }
      };
    });
  }

  /**
   * Clear all stored keys (logout)
   */
  static async clearKeys(): Promise<void> {
    const db = await KeyManager.initDB();
    const transaction = db.transaction([KeyManager.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(KeyManager.STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  /**
   * Check if Web Crypto API is available
   */
  static isSupported(): boolean {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined' &&
           typeof indexedDB !== 'undefined';
  }
}