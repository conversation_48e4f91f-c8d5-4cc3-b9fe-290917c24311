{"name": "@secure-vault/config", "version": "0.1.0", "private": true, "description": "Shared configuration for Secure File Vault", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./eslint": {"require": "./eslint.js"}, "./prettier": {"require": "./prettier.js"}, "./tailwind": {"require": "./tailwind.config.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"typescript": "^5.3.0"}}