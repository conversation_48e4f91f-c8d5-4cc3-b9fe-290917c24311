// Shared configuration constants and utilities
export const APP_CONFIG = {
  name: 'Secure File Vault',
  version: '0.1.0',
  description: 'Zero-knowledge, end-to-end encrypted file storage platform',

  // Storage limits by tier
  storage: {
    free: 5 * 1024 * 1024 * 1024, // 5GB
    professional: 1024 * 1024 * 1024 * 1024, // 1TB
    business: 5 * 1024 * 1024 * 1024 * 1024, // 5TB
  },

  // Bandwidth limits by tier (bytes per second)
  bandwidth: {
    free: 1024 * 1024, // 1 Mbps
    professional: -1, // Unlimited
    business: -1, // Unlimited
  },

  // File upload limits
  upload: {
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB per file
    allowedMimeTypes: [
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',

      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',

      // Videos
      'video/mp4',
      'video/webm',
      'video/quicktime',

      // Audio
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',

      // Archives
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
    ],
  },

  // Security settings
  security: {
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    passwordMinLength: 12,
    requireTwoFactor: false,
  },
} as const;

export type AppConfig = typeof APP_CONFIG;
