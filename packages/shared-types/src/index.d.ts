export interface User {
  id: string;
  email: string;
  encryptedMasterKey: string;
  keyDerivationParams: KeyDerivationParams;
  publicKey: string;
  storageQuota: number;
  storageUsed: number;
  tier: UserTier;
  createdAt: Date;
  lastLoginAt: Date;
  twoFactorEnabled: boolean;
}
export interface KeyDerivationParams {
  salt: string;
  iterations: number;
  algorithm: string;
}
export type UserTier = 'free' | 'professional' | 'business';
export interface FileRecord {
  id: string;
  userId: string;
  encryptedName: string;
  encryptedSize: string;
  mimeType: string;
  encryptedFileKey: string;
  storageLocation: string;
  checksum: string;
  createdAt: Date;
  modifiedAt: Date;
  folderId?: string;
  tags?: string[];
}
export interface ShareRecord {
  id: string;
  fileId: string;
  ownerId: string;
  shareToken: string;
  encryptedFileKey: string;
  permissions: SharePermissions;
  accessLog: AccessLogEntry[];
  createdAt: Date;
}
export interface SharePermissions {
  canDownload: boolean;
  canView: boolean;
  expiresAt?: Date;
  maxAccesses?: number;
  passwordProtected: boolean;
}
export interface AccessLogEntry {
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  action: 'view' | 'download';
}
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
}
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}
export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}
export interface LoginCredentials {
  email: string;
  password: string;
}
export interface RegisterData {
  email: string;
  password: string;
  encryptedMasterKey: string;
  keyDerivationParams: KeyDerivationParams;
  publicKey: string;
}
export declare enum FilePermission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  SHARE = 'share',
}
export declare enum SystemState {
  ACTIVE = 'active',
  MAINTENANCE = 'maintenance',
  DEGRADED = 'degraded',
}
//# sourceMappingURL=index.d.ts.map
