{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@shared-types/index": ["../../packages/shared-types/src/index.ts"], "@crypto-core/index": ["../../packages/crypto-core/src/index.ts"], "@ui-components/index": ["../../packages/ui-components/src/index.ts"], "@api-client/index": ["../../packages/api-client/src/index.ts"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}