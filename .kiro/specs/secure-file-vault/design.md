# Design Document

## Overview

The Secure File Vault is a zero-knowledge, end-to-end encrypted file storage platform built on a client-side encryption architecture where the server never has access to user data or encryption keys. The system employs a three-tier architecture with a Next.js web frontend, NestJS backend API, and distributed cloud storage infrastructure, organized as a monorepo for optimal code sharing and development efficiency.

The core security principle is that all cryptographic operations occur client-side, with the server acting purely as an encrypted data broker. This ensures that even with full server compromise, user data remains protected. The platform supports progressive enhancement through three development phases, starting with core MVP functionality and expanding to enterprise features.

## Architecture

### Monorepo Structure

The project is organized as a monorepo using Nx or Turborepo for optimal code sharing, consistent tooling, and efficient development workflows:

```
secure-file-vault/
├── apps/
│   ├── web/                    # Next.js frontend application
│   ├── api/                    # NestJS backend application
│   ├── mobile/                 # React Native mobile app (Phase 2)
│   └── desktop/                # Electron desktop app (Phase 2)
├── packages/
│   ├── shared-types/           # Common TypeScript interfaces and types
│   ├── crypto-core/            # Shared cryptographic utilities and functions
│   ├── ui-components/          # Reusable UI components (Radix UI + Tailwind)
│   ├── api-client/             # Shared API client and SDK
│   └── config/                 # Shared configuration (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
├── libs/
│   ├── database/               # Prisma schema and database utilities
│   ├── auth/                   # Shared authentication logic
│   └── testing/                # Shared testing utilities and fixtures
└── tools/
    ├── scripts/                # Build and deployment scripts
    └── generators/             # Code generation templates
```

**Monorepo Benefits:**

- **Code Sharing**: Cryptographic functions and types shared across web, mobile, and desktop
- **Consistent Tooling**: Unified ESLint, Prettier, and TypeScript configurations
- **Atomic Changes**: Single commit can update frontend, backend, and shared packages
- **Build Optimization**: Intelligent caching and parallel builds
- **Dependency Management**: Centralized package management with workspace protocols

**Shared Packages:**

- **crypto-core**: Zero-knowledge encryption utilities, key management, Web Crypto API wrappers
- **shared-types**: User, File, Share, and API response type definitions
- **ui-components**: Accessible Radix UI components with consistent styling
- **api-client**: Type-safe API client with automatic request/response validation

### Enterprise Considerations

**Observability & Monitoring:**

- **OpenTelemetry**: Distributed tracing across all services
- **Prometheus + Grafana**: Metrics collection and visualization
- **ELK Stack**: Centralized logging with security event correlation
- **Health Checks**: Kubernetes-ready liveness and readiness probes
- **SLA Monitoring**: 99.9% uptime targets with alerting

**Security & Compliance:**

- **Vault/HashiCorp**: Secrets management and key rotation
- **SIEM Integration**: Security event monitoring and threat detection
- **Compliance Automation**: Automated SOC2, HIPAA, GDPR compliance checks
- **Zero-Trust Architecture**: Service mesh with mTLS between all components
- **Data Loss Prevention**: Automated scanning and classification

**Scalability & Performance:**

- **Horizontal Pod Autoscaling**: Kubernetes-based auto-scaling
- **Database Sharding**: Partition strategy for multi-tenant data
- **CDN Strategy**: Global edge caching with Cloudflare/AWS CloudFront
- **Rate Limiting**: Redis-based distributed rate limiting
- **Circuit Breakers**: Resilience patterns for external dependencies

**DevOps & Infrastructure:**

- **Infrastructure as Code**: Terraform for cloud resource management
- **GitOps**: ArgoCD for declarative deployment management
- **Container Security**: Distroless images with vulnerability scanning
- **Blue-Green Deployments**: Zero-downtime deployment strategy
- **Disaster Recovery**: Multi-region backup with RTO < 4 hours, RPO < 1 hour

**Data Governance:**

- **Data Classification**: Automated PII detection and labeling
- **Retention Policies**: Automated data lifecycle management
- **Right to be Forgotten**: GDPR-compliant data deletion workflows
- **Data Lineage**: Track data flow for compliance auditing
- **Backup Encryption**: End-to-end encrypted backups with key rotation

**API Design & Integration:**

- **OpenAPI 3.0**: Comprehensive API documentation with Swagger UI
- **API Versioning**: Semantic versioning with backward compatibility guarantees
- **GraphQL Gateway**: Unified data layer for complex client requirements
- **Webhook System**: Event-driven notifications for enterprise integrations
- **SDK Development**: Official SDKs for Python, Node.js, Java, .NET
- **Enterprise SSO**: SAML 2.0, OIDC, and Active Directory integration
- **API Rate Limiting**: Tiered rate limits based on subscription plans
- **API Analytics**: Usage metrics and performance monitoring per client

**Cost Optimization & Resource Management:**

- **Multi-Tenancy**: Shared infrastructure with tenant isolation for cost efficiency
- **Storage Tiering**: Hot/warm/cold storage based on access patterns
- **Compute Optimization**: Spot instances for non-critical workloads
- **Resource Quotas**: Per-tenant resource limits with burst capabilities
- **Cost Monitoring**: Real-time cost tracking with budget alerts
- **Data Deduplication**: Block-level deduplication to reduce storage costs
- **Compression**: Intelligent compression before encryption to optimize storage
- **Auto-Scaling Policies**: Predictive scaling based on usage patterns

**Migration & Legacy Integration:**

- **Data Migration Tools**: Secure migration from existing file storage systems
- **Legacy API Adapters**: Compatibility layers for existing enterprise integrations
- **Gradual Migration Strategy**: Phased approach with parallel system operation
- **Data Validation**: Integrity checks during migration with rollback capabilities
- **Zero-Downtime Migration**: Blue-green migration strategy for enterprise clients
- **Hybrid Cloud Support**: Integration with existing on-premise infrastructure
- **Compliance Continuity**: Maintain audit trails during migration process

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Application]
        MOBILE[Mobile Apps]
        DESKTOP[Desktop Apps]
    end

    subgraph "API Gateway Layer"
        LB[Load Balancer]
        API[API Gateway]
        AUTH[Authentication Service]
    end

    subgraph "Application Layer"
        USER[User Service]
        FILE[File Service]
        SHARE[Sharing Service]
        ADMIN[Admin Service]
    end

    subgraph "Storage Layer"
        META[PostgreSQL Database]
        BLOB[Encrypted Blob Storage]
        CACHE[Redis Cache]
    end

    subgraph "Infrastructure Layer"
        CDN[Content Delivery Network]
        MONITOR[Monitoring & Logging]
        BACKUP[Backup Systems]
    end

    WEB --> LB
    MOBILE --> LB
    DESKTOP --> LB
    LB --> API
    API --> AUTH
    API --> USER
    API --> FILE
    API --> SHARE
    API --> ADMIN

    USER --> META
    FILE --> META
    FILE --> BLOB
    SHARE --> META
    ADMIN --> META

    API --> CACHE
    BLOB --> CDN

    META --> BACKUP
    BLOB --> BACKUP
```

### Security Architecture

The system implements a zero-knowledge architecture with the following key components:

1. **Client-Side Key Generation**: All encryption keys are generated in the browser using Web Crypto API
2. **Key Derivation**: User passwords are used to derive master keys using PBKDF2 with high iteration counts
3. **File Encryption**: Each file is encrypted with a unique AES-256-GCM key, which is then encrypted with the user's master key
4. **Metadata Protection**: File metadata (names, sizes) are encrypted separately from file content
5. **Secure Sharing**: Shared files use ephemeral keys that are encrypted for each recipient

## Components and Interfaces

### Frontend Components

#### Core Web Application (Next.js + TypeScript)

**Technology Stack:**

- **Next.js 14**: React framework with App Router for SSR/SSG capabilities
- **TypeScript**: Strong typing for security-critical crypto operations
- **React 18**: Component-based UI with hooks and context for state management
- **Radix UI**: Accessible, unstyled component primitives
- **Tailwind CSS**: Utility-first styling for responsive design
- **React Query**: Server state management and caching
- **NextAuth.js**: Authentication integration (configured for zero-knowledge)
- **Web Crypto API**: Browser-native cryptographic operations
- **IndexedDB**: Client-side encrypted key storage

**Application Structure:**

- **Public Pages**: Landing page, pricing, documentation (SSG for SEO)
- **Authentication Pages**: Login, register, password recovery (SSR)
- **Application Dashboard**: File management interface (CSR for interactivity)
- **Shared File Access**: Public file sharing pages (SSR for security)

**Core Components:**

- **CryptoService**: Handles all client-side encryption/decryption operations
- **FileManager**: Manages file upload, download, and organization
- **ShareManager**: Handles secure file sharing and link generation
- **AuthManager**: Manages user authentication and key derivation
- **StorageManager**: Handles local key storage and session management

**Rendering Strategy:**

- **Static Generation (SSG)**: Marketing pages, documentation, pricing
- **Server-Side Rendering (SSR)**: Authentication flows, shared file access
- **Client-Side Rendering (CSR)**: Main application dashboard for real-time interactions

#### Mobile Applications (React Native)

- Shared core crypto logic with web application
- Platform-specific secure storage integration
- Offline file caching with encryption
- Biometric authentication support

#### Desktop Applications (Electron)

- Native file system integration
- Background sync capabilities
- System tray integration
- Local encrypted file caching

### Backend Services (NestJS Architecture)

The backend uses NestJS with its modular architecture, dependency injection, and built-in security features. Each service is implemented as a NestJS module with controllers, services, and repositories following the framework's best practices.

#### NestJS Module Structure

```typescript
// Core modules
- AuthModule: Handles authentication and authorization
- UserModule: User management and profile operations
- FileModule: File storage and retrieval operations
- SharingModule: Secure file sharing functionality
- AdminModule: Administrative and compliance features (Phase 3)
- CommonModule: Shared utilities, guards, and interceptors
```

#### User Service

```typescript
@Injectable()
export class UserService {
  registerUser(email: string, encryptedMasterKey: string, keyDerivationParams: KeyDerivationParams): Promise<User>
  authenticateUser(email: string, challengeResponse: string): Promise<AuthToken>
  updateUserSettings(userId: string, settings: UserSettings): Promise<void>
  deleteUser(userId: string): Promise<void>
}

@Controller('users')
export class UserController {
  @Post('register')
  @UseGuards(RateLimitGuard)
  async register(@Body() registerDto: RegisterUserDto): Promise<UserResponseDto>

  @Post('login')
  @UseGuards(RateLimitGuard)
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto>
}
```

#### File Service

```typescript
@Injectable()
export class FileService {
  uploadFile(userId: string, encryptedFile: Blob, encryptedMetadata: FileMetadata): Promise<FileId>
  downloadFile(userId: string, fileId: string): Promise<EncryptedFile>
  deleteFile(userId: string, fileId: string): Promise<void>
  listFiles(userId: string, folderId?: string): Promise<FileMetadata[]>
  moveFile(userId: string, fileId: string, targetFolderId: string): Promise<void>
}

@Controller('files')
@UseGuards(JwtAuthGuard)
export class FileController {
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Body() metadata: FileMetadataDto): Promise<FileResponseDto>

  @Get(':fileId')
  async downloadFile(@Param('fileId') fileId: string, @Req() request: AuthenticatedRequest): Promise<StreamableFile>
}
```

#### Sharing Service

```typescript
@Injectable()
export class SharingService {
  createShareLink(userId: string, fileId: string, permissions: SharePermissions): Promise<ShareLink>
  accessSharedFile(shareToken: string, accessKey?: string): Promise<EncryptedFile>
  revokeShare(userId: string, shareId: string): Promise<void>
  createFileDropPortal(userId: string, config: FileDropConfig): Promise<FileDropPortal>
}

@Controller('shares')
export class SharingController {
  @Post()
  @UseGuards(JwtAuthGuard)
  async createShare(@Body() createShareDto: CreateShareDto): Promise<ShareResponseDto>

  @Get(':shareToken')
  async accessSharedFile(@Param('shareToken') shareToken: string, @Query('key') accessKey?: string): Promise<StreamableFile>
}
```

#### Admin Service (Phase 3)

```typescript
@Injectable()
export class AdminService {
  getUserMetrics(orgId: string): Promise<UserMetrics>
  getAuditLogs(orgId: string, filters: AuditFilters): Promise<AuditLog[]>
  manageUserAccess(orgId: string, userId: string, permissions: UserPermissions): Promise<void>
  generateComplianceReport(orgId: string, reportType: ComplianceReportType): Promise<Report>
}

@Controller('admin')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminController {
  @Get('metrics')
  async getUserMetrics(@Query() filters: MetricsFilterDto): Promise<UserMetricsDto>

  @Get('audit-logs')
  async getAuditLogs(@Query() filters: AuditFiltersDto): Promise<AuditLogDto[]>
}
```

#### Database and ORM

- **PostgreSQL**: Primary database for metadata, user accounts, and audit logs
- **Prisma**: Modern ORM with excellent TypeScript support and migration management
- **Redis**: Caching layer for session management and frequently accessed data
- **Database Features**: ACID compliance, encryption at rest, automated backups
- **Connection Pooling**: PgBouncer for connection management and high availability
- **Read Replicas**: Separate read replicas for analytics and reporting queries

#### NestJS Security Features

- **Guards**: JWT authentication, role-based access control, rate limiting
- **Interceptors**: Request/response logging, audit trail generation
- **Pipes**: Input validation and transformation using class-validator
- **Middleware**: CORS, helmet security headers, request sanitization
- **Exception Filters**: Centralized error handling and logging

## Data Models

### User Model

```typescript
interface User {
  id: string;
  email: string;
  encryptedMasterKey: string; // Encrypted with password-derived key
  keyDerivationParams: {
    salt: string;
    iterations: number;
    algorithm: string;
  };
  publicKey: string; // For sharing operations
  storageQuota: number;
  storageUsed: number;
  tier: 'free' | 'professional' | 'business';
  createdAt: Date;
  lastLoginAt: Date;
  twoFactorEnabled: boolean;
}
```

### File Model

```typescript
interface FileRecord {
  id: string;
  userId: string;
  encryptedName: string; // Encrypted filename
  encryptedSize: string; // Encrypted file size
  mimeType: string; // Encrypted MIME type
  encryptedFileKey: string; // File encryption key, encrypted with user's master key
  storageLocation: string; // Cloud storage path
  checksum: string; // Integrity verification
  createdAt: Date;
  modifiedAt: Date;
  folderId?: string;
  tags?: string[]; // Encrypted tags
}
```

### Share Model

```typescript
interface ShareRecord {
  id: string;
  fileId: string;
  ownerId: string;
  shareToken: string; // Unique access token
  encryptedFileKey: string; // File key encrypted for sharing
  permissions: {
    canDownload: boolean;
    canView: boolean;
    expiresAt?: Date;
    maxAccesses?: number;
    passwordProtected: boolean;
  };
  accessLog: AccessLogEntry[];
  createdAt: Date;
}
```

### Organization Model (Phase 3)

```typescript
interface Organization {
  id: string;
  name: string;
  adminUsers: string[];
  storageQuota: number;
  complianceSettings: {
    hipaaEnabled: boolean;
    gdprEnabled: boolean;
    auditRetentionDays: number;
    dataResidencyRegion: string;
  };
  ssoConfig?: {
    provider: 'saml' | 'oauth';
    configuration: SSOConfiguration;
  };
}
```

## Error Handling

### Client-Side Error Handling

1. **Cryptographic Errors**
   - Key derivation failures: Prompt for password re-entry
   - Decryption failures: Indicate corrupted data or wrong key
   - Browser compatibility: Fallback to polyfills or graceful degradation

2. **Network Errors**
   - Upload failures: Implement retry logic with exponential backoff
   - Download failures: Resume capability for large files
   - Connection timeouts: Queue operations for retry when connection restored

3. **Storage Errors**
   - Quota exceeded: Clear notification with upgrade options
   - File corruption: Integrity check failures with recovery options
   - Browser storage limits: Graceful cleanup of cached data

### Server-Side Error Handling

1. **Authentication Errors**
   - Invalid credentials: Rate limiting and account lockout protection
   - Token expiration: Automatic refresh with secure token rotation
   - Unauthorized access: Comprehensive audit logging

2. **Storage Errors**
   - Cloud storage failures: Multi-region redundancy and failover
   - Database connectivity: Connection pooling and retry logic
   - Backup failures: Multiple backup strategies with monitoring

3. **Compliance Errors**
   - Audit log failures: Critical system alerts and backup logging
   - Data residency violations: Automatic data location enforcement
   - Access control violations: Immediate access revocation and alerting

## Testing Strategy

### Unit Testing

- **Cryptographic Functions**: Comprehensive test coverage for all encryption/decryption operations
- **Key Management**: Test key generation, derivation, and storage mechanisms
- **File Operations**: Test upload, download, and metadata handling
- **Sharing Logic**: Test share creation, access, and revocation
- **Error Scenarios**: Test all error conditions and recovery mechanisms

### Integration Testing

- **End-to-End Encryption**: Verify data remains encrypted throughout the entire flow
- **Cross-Platform Compatibility**: Test crypto operations across different browsers and devices
- **API Integration**: Test all client-server interactions
- **Storage Integration**: Test cloud storage operations and failover scenarios
- **Authentication Flow**: Test complete user registration and login processes

### Security Testing

- **Penetration Testing**: Regular third-party security assessments
- **Cryptographic Audits**: Independent review of encryption implementation
- **Zero-Knowledge Verification**: Prove server cannot access user data
- **Side-Channel Analysis**: Test for timing attacks and information leakage
- **Compliance Testing**: Verify HIPAA, GDPR, and other regulatory requirements

### Performance Testing

- **Encryption Overhead**: Measure and optimize cryptographic performance
- **Large File Handling**: Test upload/download of files up to storage limits
- **Concurrent Users**: Load testing for multi-user scenarios
- **Mobile Performance**: Optimize for mobile device constraints
- **Network Conditions**: Test under various network conditions and bandwidth limits

### User Acceptance Testing

- **Usability Testing**: Ensure crypto complexity doesn't impact user experience
- **Cross-Browser Testing**: Verify functionality across all supported browsers
- **Accessibility Testing**: Ensure WCAG compliance for all user interfaces
- **Persona-Based Testing**: Test specific workflows for journalists, activists, and business users
- **Recovery Scenarios**: Test password recovery and account restoration processes

### Automated Testing Pipeline

- **Continuous Integration**: Automated test execution on every code change
- **Security Scanning**: Automated vulnerability scanning and dependency checking
- **Performance Monitoring**: Continuous performance regression testing
- **Compliance Validation**: Automated checks for regulatory requirement adherence
- **Cross-Platform Testing**: Automated testing across multiple browsers and devices

### Enterprise Testing Strategy

- **Chaos Engineering**: Netflix Chaos Monkey for resilience testing
- **Load Testing**: K6 scripts for realistic traffic simulation
- **Contract Testing**: Pact for API contract validation between services
- **Synthetic Monitoring**: Datadog/New Relic for production health checks
- **A/B Testing**: Feature flags with statistical significance testing
- **Canary Deployments**: Gradual rollouts with automated rollback triggers
- **Penetration Testing**: Quarterly third-party security assessments
- **Compliance Testing**: Automated SOC2 Type II and HIPAA validation

### Quality Gates

- **Code Coverage**: Minimum 85% test coverage with quality metrics
- **Security Gates**: SAST/DAST scanning with zero critical vulnerabilities
- **Performance Gates**: Sub-200ms API response times, 99.9% uptime
- **Accessibility Gates**: WCAG 2.1 AA compliance validation
- **Documentation Gates**: API documentation completeness checks

The testing strategy emphasizes security-first validation while ensuring the user experience remains intuitive despite the underlying cryptographic complexity. All tests must verify that the zero-knowledge architecture is maintained and that user data remains protected under all conditions.
