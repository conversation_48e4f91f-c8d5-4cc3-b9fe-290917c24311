{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}}}