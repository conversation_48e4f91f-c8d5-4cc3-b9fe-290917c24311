{"name": "@secure-vault/auth", "version": "0.1.0", "private": true, "description": "Authentication utilities for Secure File Vault", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@secure-vault/shared-types": "*", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "typescript": "^5.3.0"}}