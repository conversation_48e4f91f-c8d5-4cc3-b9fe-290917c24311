{"name": "@secure-vault/database", "version": "0.1.0", "private": true, "description": "Database utilities and Prisma schema for Secure File Vault", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.0", "@secure-vault/shared-types": "*"}, "devDependencies": {"prisma": "^5.7.0", "typescript": "^5.3.0"}}