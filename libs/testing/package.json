{"name": "@secure-vault/testing", "version": "0.1.0", "private": true, "description": "Testing utilities for Secure File Vault", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@secure-vault/shared-types": "*", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0"}, "devDependencies": {"@types/jest": "^29.5.0", "typescript": "^5.3.0"}}